# Build configuration
# -------------------

APP_NAME := `sed -n 's/^ *name.*=.*"\([^"]*\)".*/\1/p' pyproject.toml`
APP_VERSION := `sed -n 's/^ *version.*=.*"\([^"]*\)".*/\1/p' pyproject.toml`
GIT_REVISION = `git rev-parse HEAD`

# Introspection targets
# ---------------------

.PHONY: help
help: header targets

.PHONY: header
header:
	@echo "\033[34mEnvironment\033[0m"
	@echo "\033[34m---------------------------------------------------------------\033[0m"
	@printf "\033[33m%-23s\033[0m" "APP_NAME"
	@printf "\033[35m%s\033[0m" $(APP_NAME)
	@echo ""
	@printf "\033[33m%-23s\033[0m" "APP_VERSION"
	@printf "\033[35m%s\033[0m" $(APP_VERSION)
	@echo ""
	@printf "\033[33m%-23s\033[0m" "GIT_REVISION"
	@printf "\033[35m%s\033[0m" $(GIT_REVISION)
	@echo "\n"

.PHONY: targets
targets:
	@echo "\033[34mDevelopment Targets\033[0m"
	@echo "\033[34m---------------------------------------------------------------\033[0m"
	@perl -nle'print $& if m{^[a-zA-Z_-]+:.*?## .*$$}' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-22s\033[0m %s\n", $$1, $$2}'

# Development targets
# -------------

.PHONY: install
install: ## Install dependencies
	uv add pyproject.toml


.PHONY: dev
dev: start

.PHONY: start
start: ## Starts the server
	python run.py

# Check, lint and format targets
# ------------------------------

.PHONY: check
check: check-format lint

.PHONY: check-format
check-format: ## Dry-run code formatter
	black ./ --check
	isort ./ --profile black --check

.PHONY: lint
lint: ## Run ruff
	ruff check ./
 
.PHONY: format
format: ## Run code formatter
	black ./
	isort ./ --profile black


.PHONY: test
test: ## Run the test suite
	$(eval include .env)
	$(eval export $(shell sed 's/=.*//' .env))
	pytest -vv -s --cache-clear ./

# Database management targets
# ---------------------------

.PHONY: db-quick
db-quick: ## 快速初始化数据库（开发环境推荐）
	python scripts/quick_init.py

.PHONY: db-init
db-init: ## 初始化数据库（安全模式，保留现有数据）
	python scripts/init_database.py

.PHONY: db-reset
db-reset: ## 重置数据库（危险操作，删除所有数据）
	python scripts/init_database.py --drop-tables

.PHONY: db-tables
db-tables: ## 只创建表结构，跳过数据初始化
	python scripts/init_database.py --skip-data

.PHONY: db-verify
db-verify: ## 验证数据库状态
	python scripts/init_database.py --verify-only

.PHONY: db-check
db-check: ## 检查数据库连接和状态
	python scripts/check_database.py

.PHONY: db-force-reset
db-force-reset: ## 强制重置数据库（不询问确认）
	python scripts/init_database.py --drop-tables --force

.PHONY: clean-db
clean-db: ## 删除migrations文件夹和db.sqlite3
	find . -type d -name "migrations" -exec rm -rf {} +
	rm -f db.sqlite3 db.sqlite3-shm db.sqlite3-wal

.PHONY: migrate
migrate: ## 运行aerich migrate命令生成迁移文件
	aerich migrate

.PHONY: upgrade
upgrade: ## 运行aerich upgrade命令应用迁移
	aerich upgrade

# Task execution targets
# ----------------------

.PHONY: task-help
task-help: ## 显示任务系统使用说明
	@echo "🎯 任务系统可用命令:"
	@echo ""
	@echo "📊 趋势刷新任务:"
	@echo "  make task-trend-refresh       - 执行标准趋势刷新任务"
	@echo "  make task-trend-refresh-small - 执行小批量趋势刷新任务"
	@echo ""
	@echo "👥 作者监控任务:"
	@echo "  make task-author-monitor      - 执行标准作者监控任务"
	@echo "  make task-author-monitor-large- 执行大批量作者监控任务"
	@echo ""
	@echo "🔍 关键词监控任务:"
	@echo "  make task-keyword-monitor     - 执行标准关键词监控任务"
	@echo "  make task-keyword-monitor-weekly - 执行7天范围关键词监控任务"
	@echo ""
	@echo "🚀 组合任务:"
	@echo "  make task-all                 - 依次执行所有任务"
	@echo "  make task-test                - 执行测试用小规模任务"
	@echo ""
	@echo "💡 手动执行示例:"
	@echo "  python tasks/main.py '{\"task_type\": \"trend_refresh\", \"batch_size\": 100}'"

.PHONY: task-check
task-check: ## 检查任务系统环境和依赖
	@echo "🔍 检查任务系统环境..."
	@echo ""
	@echo "📁 检查项目结构:"
	@test -f tasks/main.py && echo "✅ tasks/main.py 存在" || echo "❌ tasks/main.py 不存在"
	@test -d core && echo "✅ core 目录存在" || echo "❌ core 目录不存在"
	@test -f core/crud.py && echo "✅ core/crud.py 存在" || echo "❌ core/crud.py 不存在"
	@echo ""
	@echo "🐍 检查 Python 环境:"
	@python --version
	@echo ""
	@echo "📦 检查关键依赖:"
	@python -c "import sys; print('✅ Python 路径:', sys.path[0])" 2>/dev/null || echo "❌ Python 路径检查失败"
	@python -c "import fastapi; print('✅ FastAPI 可用')" 2>/dev/null || echo "❌ FastAPI 不可用"
	@python -c "import tortoise; print('✅ Tortoise ORM 可用')" 2>/dev/null || echo "❌ Tortoise ORM 不可用"
	@echo ""
	@echo "🎯 建议检查数据库连接: make db-check"

.PHONY: task-trend-refresh
task-trend-refresh: ## 执行趋势刷新任务
	cd . && python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 100}'

.PHONY: task-trend-refresh-small
task-trend-refresh-small: ## 执行趋势刷新任务（小批量）
	cd . && python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 50, "max_age_hours": 2}'

.PHONY: task-author-monitor
task-author-monitor: ## 执行作者监控任务
	cd . && python tasks/main.py '{"task_type": "author_monitor", "batch_size": 50, "author_video_limit": 50}'

.PHONY: task-author-monitor-large
task-author-monitor-large: ## 执行作者监控任务（大批量）
	cd . && python tasks/main.py '{"task_type": "author_monitor", "batch_size": 100, "author_video_limit": 100}'

.PHONY: task-keyword-monitor
task-keyword-monitor: ## 执行关键词监控任务
	cd . && python tasks/main.py '{"task_type": "keyword_monitor", "batch_size": 20, "keyword_video_limit": 100}'

.PHONY: task-keyword-monitor-weekly
task-keyword-monitor-weekly: ## 执行关键词监控任务（7天范围）
	cd . && python tasks/main.py '{"task_type": "keyword_monitor", "batch_size": 30, "keyword_video_limit": 150, "keyword_search_days": 7}'

.PHONY: task-all
task-all: ## 依次执行所有任务
	@echo "🚀 执行趋势刷新任务..."
	@$(MAKE) task-trend-refresh
	@echo "✅ 趋势刷新任务完成"
	@echo ""
	@echo "🚀 执行作者监控任务..."
	@$(MAKE) task-author-monitor
	@echo "✅ 作者监控任务完成"
	@echo ""
	@echo "🚀 执行关键词监控任务..."
	@$(MAKE) task-keyword-monitor
	@echo "✅ 关键词监控任务完成"
	@echo ""
	@echo "🎉 所有任务执行完成！"

.PHONY: task-test
task-test: ## 执行测试用的小规模任务
	cd . && python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 10, "timeout": 300}'
