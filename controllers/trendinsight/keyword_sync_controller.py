"""
TrendInsight 关键词同步控制器

负责同步关键词相关视频列表的业务逻辑
"""

import hashlib
import logging
from typing import List, Optional

from fastapi import HTTPException

from mappers.trendinsight import TrendInsightKeywordMapper, TrendInsightVideoMapper
from models.enums import KeywordActionType, Platform, SourceType
from models.trendinsight.models import TrendInsightKeyword, TrendInsightVideoRelated
from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from rpc.trendinsight.config import TrendInsightConfig
from rpc.trendinsight.schemas import VideoSearchResponse
from schemas.trendinsight import DouyinAwemeData, KeywordData, KeywordSyncResponse


class KeywordSyncController:
    """TrendInsight 关键词同步控制器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = TrendInsightConfig()

    def _calculate_keyword_hash(self, keyword: str) -> str:
        """
        计算关键词的哈希值

        Args:
            keyword: 关键词文本

        Returns:
            str: MD5 哈希值
        """
        return hashlib.md5(keyword.encode("utf-8")).hexdigest()

    async def _get_trendinsight_client(self) -> AsyncTrendInsightAPI:
        """
        获取 TrendInsight 异步客户端实例

        Returns:
            AsyncTrendInsightAPI: 配置好账号提供者的异步客户端实例
        """
        # 通过 client_manager 创建客户端，cookies 由账号提供者自动管理
        async_client = client_manager.create_async_client()
        return AsyncTrendInsightAPI(async_client=async_client)

    async def search_info_by_keyword(
        self,
        keyword: str,
        author_ids: Optional[List[str]] = None,
        category_id: str = "0",
        date_type: int = 0,
        label_type: int = 0,
        duration_type: int = 0,
    ) -> VideoSearchResponse:
        """
        关键字搜索视频接口

        Args:
            keyword: 搜索关键词
            author_ids: 作者ID列表，可选
            category_id: 分类ID，默认为"0"
            date_type: 日期类型，默认为0
            label_type: 标签类型，默认为0
            duration_type: 时长类型，默认为0

        Returns:
            VideoSearchResponse: 视频搜索响应
        """
        try:
            client = await self._get_trendinsight_client()

            response = await client.search_info_by_keyword(
                keyword=keyword,
                author_ids=author_ids,
                category_id=category_id,
                date_type=date_type,
                label_type=label_type,
                duration_type=duration_type,
            )
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"搜索视频失败: {str(e)}")

    async def sync_keyword_videos(self, keyword: str) -> KeywordSyncResponse:
        """
        同步关键词相关的视频列表

        功能：
        1. 计算关键词哈希，检查 trendinsight_keyword 表中是否存在，不存在则创建
        2. 使用 TrendInsight RPC 关键词搜索视频接口查询相关视频
        3. 在 trendinsight_video_related 表中创建关联记录（不存在才创建）

        Args:
            keyword: 搜索关键词文本

        Returns:
            KeywordSyncResponse: 同步结果信息

        Example:
            {
                "keyword_action": "created",
                "keyword_data": {
                    "id": 123,
                    "keyword": "科技前沿",
                    "keyword_hash": "abc123def456",
                    "video_count": 25,
                    "created_at": "2025-01-01T00:00:00",
                    "updated_at": "2025-01-01T00:00:00"
                },
                "videos_synced": 25,
                "videos_failed": 0,
                "relations_created": 20,
                "relations_existing": 5,
                "video_items": [
                    {
                        "aweme_id": "7123456789012345678",
                        "aweme_type": "video",
                        "title": "科技前沿视频",
                        "desc": "这是一个关于科技前沿的视频",
                        "create_time": 1705314645,
                        "user_id": "123456789",
                        "nickname": "科技博主",
                        "liked_count": "1000",
                        "comment_count": "50",
                        "share_count": "20",
                        "collected_count": "30",
                        "source_keyword": "科技前沿"
                    }
                ],
                "errors": []
            }
        """
        try:
            # 初始化 KeywordSyncResponse 实例，提供类型安全性
            sync_result = KeywordSyncResponse(
                keyword_action=KeywordActionType.EXISTING,  # 默认值，后续会更新
                keyword_data=None,
                videos_synced=0,
                videos_failed=0,
                relations_created=0,
                relations_existing=0,
                video_items=[],
                errors=[],
            )

            # 1. 计算关键词哈希并检查是否存在
            keyword_hash: str = self._calculate_keyword_hash(keyword)
            existing_keyword: Optional[TrendInsightKeyword] = await TrendInsightKeyword.filter(
                keyword_hash=keyword_hash
            ).first()

            keyword_record: Optional[TrendInsightKeyword] = None
            if existing_keyword:
                keyword_record = existing_keyword
                sync_result.keyword_action = KeywordActionType.EXISTING
                sync_result.keyword_data = TrendInsightKeywordMapper.keyword_model_to_data(existing_keyword)
                print(f"关键词已存在: {keyword} (ID: {existing_keyword.id})")
            else:
                # 创建新的关键词记录
                try:
                    new_keyword = await TrendInsightKeyword.create(
                        keyword=keyword,
                        keyword_hash=keyword_hash,
                        video_count=0,
                    )

                    keyword_record = new_keyword
                    sync_result.keyword_action = KeywordActionType.CREATED
                    sync_result.keyword_data = TrendInsightKeywordMapper.keyword_model_to_data(new_keyword)
                    print(f"关键词创建成功: {keyword} (ID: {new_keyword.id})")

                except Exception as e:
                    error_msg: str = f"创建关键词记录失败: {str(e)}"
                    sync_result.errors.append(error_msg)
                    print(error_msg)
                    return sync_result

            # 2. 使用 TrendInsight RPC 搜索关键词相关视频
            try:
                video_search_response = await self.search_info_by_keyword(
                    keyword=keyword, author_ids=None, category_id="0", date_type=0, label_type=0, duration_type=0
                )

                if video_search_response and video_search_response.is_success and video_search_response.video_items:
                    # 3. 批量检查和创建视频关联记录
                    # 智能批量处理 DouyinAweme 表记录（创建和更新）
                    try:
                        print(f"开始智能处理 DouyinAweme 记录，关键词: {keyword}")

                        # 使用mapper转换数据为完整的DouyinAwemeData格式
                        video_data_list, video_ids = TrendInsightVideoMapper.keyword_videos_to_douyin_aweme_data_list(
                            videos=video_search_response.video_items, source_keyword=keyword
                        )

                        # 过滤出 index > 10 的视频数据
                        filtered_video_data_list: List[DouyinAwemeData] = []
                        filtered_video_ids: List[str] = []

                        for i, video_data in enumerate(video_data_list):
                            # 检查 index 字段并转换为数字进行比较
                            if video_data.index is not None:
                                try:
                                    # 将 index 转换为数字（可能是字符串或数字）
                                    index_value = float(video_data.index) if isinstance(video_data.index, str) else video_data.index
                                    if index_value > 10:
                                        filtered_video_data_list.append(video_data)
                                        # 确保 video_ids 和 video_data_list 索引对应
                                        if i < len(video_ids):
                                            filtered_video_ids.append(video_ids[i])
                                except (ValueError, TypeError):
                                    # 如果无法转换为数字，跳过这个视频
                                    continue

                        print(f"原始视频数量: {len(video_data_list)}, 过滤后数量 (index > 10): {len(filtered_video_data_list)}")

                        # 将过滤后的视频数据赋值给响应（符合类型注解 List[DouyinAwemeData]）
                        sync_result.video_items = filtered_video_data_list

                        # 使用服务层处理数据库操作（使用过滤后的数据）
                        from controllers.trendinsight.services import DouyinAwemeService

                        aweme_created, aweme_existing = await DouyinAwemeService.ensure_douyin_aweme_records(
                            video_data_list=filtered_video_data_list, video_ids=filtered_video_ids
                        )
                        print(f"DouyinAweme 智能处理完成 - 新创建: {aweme_created}, 已存在/更新: {aweme_existing}")

                        # 更新同步结果统计（使用过滤后的数量）
                        sync_result.videos_synced = len(filtered_video_ids)

                    except Exception as e:
                        error_msg: str = f"DouyinAweme 表智能批量处理失败: {str(e)}"
                        sync_result.errors.append(error_msg)
                        print(error_msg)
                        # 记录更多详细错误信息用于调试
                        import traceback

                        print(f"详细错误堆栈: {traceback.format_exc()}")

                    if filtered_video_ids:
                        print(f"开始处理关联关系，共 {len(filtered_video_ids)} 个视频 (index > 10)")

                        # 批量检查已存在的关联记录（性能优化：一次查询）
                        existing_relations: List[str] = await TrendInsightVideoRelated.filter(
                            source_id=keyword_record.id, video_id__in=filtered_video_ids
                        ).values_list("video_id", flat=True)

                        existing_video_ids: set[str] = set(existing_relations)
                        sync_result.relations_existing = len(existing_video_ids)

                        print(f"已存在关联: {len(existing_video_ids)} 个")

                        # 使用集合运算计算需要创建的新关联（性能优化）
                        all_video_ids = set(filtered_video_ids)
                        new_video_ids = all_video_ids - existing_video_ids

                        print(f"需要创建新关联: {len(new_video_ids)} 个")

                        # 准备批量创建的数据
                        new_relations: List[TrendInsightVideoRelated] = []
                        for video_id in new_video_ids:
                            new_relation = TrendInsightVideoRelated(
                                source_type=SourceType.KEYWORD,
                                source_id=keyword_record.id,
                                video_id=video_id,
                                platform=Platform.DOUYIN,
                            )
                            new_relations.append(new_relation)

                        # 批量创建新关联记录
                        if new_relations:
                            try:
                                await TrendInsightVideoRelated.bulk_create(new_relations)
                                sync_result.relations_created = len(new_relations)
                                print(f"成功创建 {len(new_relations)} 个新的关键词视频关联记录")

                            except Exception as e:
                                error_msg: str = f"批量创建关键词关联记录失败: {str(e)}"
                                sync_result.errors.append(error_msg)
                                print(error_msg)
                                # 添加详细错误信息
                                import traceback

                                print(f"关联创建错误详情: {traceback.format_exc()}")
                        else:
                            sync_result.relations_created = 0
                            print(f"所有关键词视频关联已存在，无需创建新关联")

                        # 更新视频同步统计（确保统计准确性）
                        if sync_result.videos_synced == 0:
                            sync_result.videos_synced = len(filtered_video_ids)

                        # 智能更新关键词的视频总数（使用过滤后的数量）
                        current_video_count: int = len(filtered_video_ids)
                        if keyword_record.video_count != current_video_count:
                            keyword_record.video_count = current_video_count
                            await keyword_record.save()
                            print(f"更新关键词视频总数 (index > 10): {keyword} -> {current_video_count} 个视频")

                            # 更新响应数据中的视频数量
                            if sync_result.keyword_data:
                                sync_result.keyword_data = TrendInsightKeywordMapper.keyword_model_to_data(keyword_record)
                        else:
                            print(f"关键词视频总数无变化，保持: {current_video_count} 个视频")
                    else:
                        error_msg: str = f"搜索结果中没有有效的视频ID: {keyword}"
                        sync_result.errors.append(error_msg)
                        print(error_msg)
                else:
                    error_msg: str = f"关键词搜索视频结果为空: {keyword}"
                    sync_result.errors.append(error_msg)
                    print(error_msg)

            except Exception as e:
                error_msg: str = f"搜索关键词视频失败: {str(e)}"
                sync_result.errors.append(error_msg)
                print(error_msg)

            return sync_result

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"同步关键词和视频失败: {str(e)}")


# 创建控制器实例
keyword_sync_controller = KeywordSyncController()
